<?php
/**
 * CSRF Token Generator
 * 
 * Generates and returns a CSRF token for form security
 */

// Define access constant
define('PORTFOLIO_ACCESS', true);

// Include configuration
require_once '../config/config.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Start session
session_start();

try {
    // Generate CSRF token
    $token = generateCSRFToken();
    
    echo json_encode([
        'success' => true,
        'token' => $token
    ]);
    
} catch (Exception $e) {
    logError("CSRF token generation error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Could not generate security token'
    ]);
}
