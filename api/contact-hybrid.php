<?php
/**
 * Static Portfolio Contact Handler
 *
 * Lightweight contact form handler for static websites with:
 * - Google Forms integration as primary submission method
 * - Optional database logging for backup
 * - Automated thank you emails with Space Phoenix theme
 * - Minimal server-side processing to maintain static nature
 */

// Define access constant
define('PORTFOLIO_ACCESS', true);

// Include configuration
require_once '../config/config.php';

// Include email template
require_once '../templates/email-template.php';

// Set content type for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get and sanitize input data (simplified for static website)
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        $input = $_POST;
    }

    $name = sanitizeInput($input['name'] ?? '');
    $email = sanitizeInput($input['email'] ?? '');
    $message = sanitizeInput($input['message'] ?? '');
    $recaptchaResponse = $input['g-recaptcha-response'] ?? '';

    // Basic validation (minimal for static site)
    $errors = [];

    // reCAPTCHA validation if enabled
    if (getConfig('recaptcha.enabled', false)) {
        if (empty($recaptchaResponse)) {
            $errors[] = 'Please complete the reCAPTCHA verification.';
        } else {
            $recaptchaValid = verifyRecaptcha($recaptchaResponse);
            if (!$recaptchaValid) {
                $errors[] = 'reCAPTCHA verification failed. Please try again.';
            }
        }
    }

    if (empty($name) || strlen($name) < 2) {
        $errors[] = 'Please enter a valid name.';
    }

    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }

    if (empty($message) || strlen($message) < 10) {
        $errors[] = 'Please enter a message with at least 10 characters.';
    }

    // Simple rate limiting using file-based approach (no session needed)
    $rateLimitFile = '../logs/rate_limit_' . md5($_SERVER['REMOTE_ADDR']) . '.txt';
    if (file_exists($rateLimitFile)) {
        $lastSubmission = (int)file_get_contents($rateLimitFile);
        if (time() - $lastSubmission < 60) { // 1 minute cooldown
            $errors[] = 'Please wait before sending another message.';
        }
    }

    if (!empty($errors)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Please correct the following errors:',
            'errors' => $errors
        ]);
        exit;
    }

    // Basic spam detection
    $spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'bitcoin', 'crypto'];
    $messageWords = strtolower($message . ' ' . $name);
    foreach ($spamKeywords as $keyword) {
        if (strpos($messageWords, $keyword) !== false) {
            logError("Potential spam detected from {$_SERVER['REMOTE_ADDR']}: $keyword");
            // Return success to avoid revealing spam detection
            echo json_encode([
                'success' => true,
                'message' => 'Thank you for your message! I\'ll get back to you soon.'
            ]);
            exit;
        }
    }
    
    // Process the submission (simplified for static website)
    $success = true;
    $details = [];

    try {
        // 1. Submit to Google Forms (primary method)
        $googleFormsSent = submitToGoogleForms($name, $email, $message);
        $details['google_forms_sent'] = $googleFormsSent;

        // 2. Optional: Save to database for backup (if configured)
        $submissionId = saveContactSubmission($name, $email, $message);
        $details['database_saved'] = $submissionId !== null;

        // 3. Send thank you email to user
        $emailSent = sendThankYouEmail($name, $email, $message);
        $details['thank_you_email_sent'] = $emailSent;

        // 4. Optional: Send notification to site owner (if email is configured)
        if (getConfig('mail.smtp_username')) {
            sendNotificationEmail($name, $email, $message, $submissionId);
        }

        // Update rate limiting
        file_put_contents($rateLimitFile, time());

        // Log successful submission
        logError("Contact form submitted from {$_SERVER['REMOTE_ADDR']} - $name ($email)", 'INFO');

        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your message! I\'ll get back to you soon. 🚀',
            'details' => $details
        ]);

    } catch (Exception $e) {
        logError("Contact form processing error: " . $e->getMessage());

        // Always return success to user (graceful degradation)
        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your message! I\'ll get back to you soon.'
        ]);
    }
    
} catch (Exception $e) {
    logError("Contact form error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Sorry, there was an error sending your message. Please try again later or contact me directly.'
    ]);
}

/**
 * Save contact submission to database
 */
function saveContactSubmission($name, $email, $message) {
    try {
        // Only save if database is configured
        if (getConfig('database.host') && getConfig('database.name')) {
            $pdo = new PDO(
                'mysql:host=' . getConfig('database.host') . ';dbname=' . getConfig('database.name') . ';charset=' . getConfig('database.charset'),
                getConfig('database.username'),
                getConfig('database.password'),
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
            
            $stmt = $pdo->prepare("
                INSERT INTO contact_submissions (name, email, message, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $name,
                $email,
                $message,
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            return $pdo->lastInsertId();
        }
        return null;
    } catch (Exception $e) {
        logError("Database error in contact form: " . $e->getMessage());
        return null;
    }
}

/**
 * Submit to Google Forms (if configured)
 */
function submitToGoogleForms($name, $email, $message) {
    try {
        // This would be configured via environment variables or config
        $googleFormUrl = getConfig('google_forms.url');
        $nameField = getConfig('google_forms.name_field');
        $emailField = getConfig('google_forms.email_field');
        $messageField = getConfig('google_forms.message_field');
        
        if (!$googleFormUrl || !$nameField || !$emailField || !$messageField) {
            return false; // Not configured
        }
        
        $postData = [
            $nameField => $name,
            $emailField => $email,
            $messageField => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $googleFormUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $httpCode === 200;
        
    } catch (Exception $e) {
        logError("Google Forms submission error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send thank you email with Space Phoenix theme
 */
function sendThankYouEmail($name, $email, $message) {
    try {
        $subject = "Thank you for reaching out! 🚀";
        $emailTemplate = getSpacePhoenixEmailTemplate($name, $message);
        
        $headers = [
            'From' => getConfig('site.name') . ' <' . getConfig('site.email') . '>',
            'Reply-To' => getConfig('site.email'),
            'X-Mailer' => 'PHP/' . phpversion(),
            'MIME-Version' => '1.0',
            'Content-Type' => 'text/html; charset=UTF-8'
        ];
        
        $headerString = '';
        foreach ($headers as $key => $value) {
            $headerString .= "$key: $value\r\n";
        }
        
        return mail($email, $subject, $emailTemplate, $headerString);
        
    } catch (Exception $e) {
        logError("Thank you email error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send notification email to site owner
 */
function sendNotificationEmail($name, $email, $message, $submissionId) {
    try {
        $subject = "New Portfolio Contact: $name";
        $emailBody = "
        New contact form submission:

        ID: $submissionId
        Name: $name
        Email: $email
        IP: {$_SERVER['REMOTE_ADDR']}
        Time: " . date('Y-m-d H:i:s') . "

        Message:
        $message
        ";

        $headers = [
            'From' => getConfig('site.email'),
            'Reply-To' => $email,
            'X-Mailer' => 'PHP/' . phpversion(),
            'Content-Type' => 'text/plain; charset=UTF-8'
        ];

        $headerString = '';
        foreach ($headers as $key => $value) {
            $headerString .= "$key: $value\r\n";
        }

        return mail(getConfig('site.email'), $subject, $emailBody, $headerString);

    } catch (Exception $e) {
        logError("Notification email error: " . $e->getMessage());
        return false;
    }
}

/**
 * Verify reCAPTCHA response
 */
function verifyRecaptcha($recaptchaResponse) {
    try {
        $secretKey = getConfig('recaptcha.secret_key');
        if (empty($secretKey)) {
            logError("reCAPTCHA secret key not configured");
            return false;
        }

        $verifyUrl = 'https://www.google.com/recaptcha/api/siteverify';
        $data = [
            'secret' => $secretKey,
            'response' => $recaptchaResponse,
            'remoteip' => $_SERVER['REMOTE_ADDR']
        ];

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];

        $context = stream_context_create($options);
        $result = file_get_contents($verifyUrl, false, $context);

        if ($result === false) {
            logError("Failed to verify reCAPTCHA - network error");
            return false;
        }

        $response = json_decode($result, true);

        if (!$response || !isset($response['success'])) {
            logError("Invalid reCAPTCHA response format");
            return false;
        }

        if (!$response['success']) {
            $errorCodes = isset($response['error-codes']) ? implode(', ', $response['error-codes']) : 'unknown';
            logError("reCAPTCHA verification failed: " . $errorCodes);
            return false;
        }

        return true;

    } catch (Exception $e) {
        logError("reCAPTCHA verification error: " . $e->getMessage());
        return false;
    }
}
