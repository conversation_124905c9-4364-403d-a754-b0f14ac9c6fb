<?php
/**
 * Hybrid Contact Form Handler
 * 
 * Handles contact form submissions with:
 * - Google Forms integration for backup/redundancy
 * - Database storage for record keeping
 * - Automated thank you emails with Space Phoenix theme
 */

// Define access constant
define('PORTFOLIO_ACCESS', true);

// Include configuration
require_once '../config/config.php';

// Include email template
require_once '../templates/email-template.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Start session for CSRF protection
session_start();

try {
    // Get and sanitize input data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }
    
    $name = sanitizeInput($input['name'] ?? '');
    $email = sanitizeInput($input['email'] ?? '');
    $message = sanitizeInput($input['message'] ?? '');
    $csrfToken = $input['csrf_token'] ?? '';
    
    // Validation
    $errors = [];
    
    // CSRF token validation
    if (!verifyCSRFToken($csrfToken)) {
        $errors[] = 'Invalid security token. Please refresh the page and try again.';
    }
    
    // Name validation
    if (empty($name)) {
        $errors[] = 'Name is required.';
    } elseif (strlen($name) < 2) {
        $errors[] = 'Name must be at least 2 characters long.';
    } elseif (strlen($name) > 100) {
        $errors[] = 'Name must be less than 100 characters.';
    }
    
    // Email validation
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    } elseif (strlen($email) > 255) {
        $errors[] = 'Email address is too long.';
    }
    
    // Message validation
    if (empty($message)) {
        $errors[] = 'Message is required.';
    } elseif (strlen($message) < 10) {
        $errors[] = 'Message must be at least 10 characters long.';
    } elseif (strlen($message) > 5000) {
        $errors[] = 'Message must be less than 5000 characters.';
    }
    
    // Rate limiting (simple implementation)
    $rateLimitKey = 'contact_' . $_SERVER['REMOTE_ADDR'];
    if (isset($_SESSION[$rateLimitKey])) {
        $lastSubmission = $_SESSION[$rateLimitKey];
        if (time() - $lastSubmission < 60) { // 1 minute cooldown
            $errors[] = 'Please wait before sending another message.';
        }
    }
    
    // If there are validation errors, return them
    if (!empty($errors)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // Spam detection (basic)
    $spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations'];
    $messageWords = strtolower($message);
    foreach ($spamKeywords as $keyword) {
        if (strpos($messageWords, $keyword) !== false) {
            logError("Potential spam detected from {$_SERVER['REMOTE_ADDR']}: $keyword");
            // Don't tell the user it was flagged as spam
            echo json_encode([
                'success' => true,
                'message' => 'Thank you for your message! I\'ll get back to you soon.'
            ]);
            exit;
        }
    }
    
    // Process the submission
    $submissionId = null;
    $emailSent = false;
    $googleFormsSent = false;
    
    try {
        // 1. Save to database
        $submissionId = saveContactSubmission($name, $email, $message);
        
        // 2. Send to Google Forms (if configured)
        $googleFormsSent = submitToGoogleForms($name, $email, $message);
        
        // 3. Send thank you email
        $emailSent = sendThankYouEmail($name, $email, $message);
        
        // 4. Send notification email to site owner
        sendNotificationEmail($name, $email, $message, $submissionId);
        
        // Log successful submission
        logError("Contact form submitted successfully from {$_SERVER['REMOTE_ADDR']} - $name ($email) - ID: $submissionId", 'INFO');
        
        // Set rate limiting
        $_SESSION[$rateLimitKey] = time();
        
        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your message! I\'ll get back to you soon.',
            'details' => [
                'database_saved' => $submissionId !== null,
                'google_forms_sent' => $googleFormsSent,
                'thank_you_email_sent' => $emailSent
            ]
        ]);
        
    } catch (Exception $e) {
        logError("Contact form processing error: " . $e->getMessage());
        
        // Even if some parts fail, we want to give a positive response to the user
        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your message! I\'ll get back to you soon.'
        ]);
    }
    
} catch (Exception $e) {
    logError("Contact form error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Sorry, there was an error sending your message. Please try again later or contact me directly.'
    ]);
}

/**
 * Save contact submission to database
 */
function saveContactSubmission($name, $email, $message) {
    try {
        // Only save if database is configured
        if (getConfig('database.host') && getConfig('database.name')) {
            $pdo = new PDO(
                'mysql:host=' . getConfig('database.host') . ';dbname=' . getConfig('database.name') . ';charset=' . getConfig('database.charset'),
                getConfig('database.username'),
                getConfig('database.password'),
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
            
            $stmt = $pdo->prepare("
                INSERT INTO contact_submissions (name, email, message, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $name,
                $email,
                $message,
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            return $pdo->lastInsertId();
        }
        return null;
    } catch (Exception $e) {
        logError("Database error in contact form: " . $e->getMessage());
        return null;
    }
}

/**
 * Submit to Google Forms (if configured)
 */
function submitToGoogleForms($name, $email, $message) {
    try {
        // This would be configured via environment variables or config
        $googleFormUrl = getConfig('google_forms.url');
        $nameField = getConfig('google_forms.name_field');
        $emailField = getConfig('google_forms.email_field');
        $messageField = getConfig('google_forms.message_field');
        
        if (!$googleFormUrl || !$nameField || !$emailField || !$messageField) {
            return false; // Not configured
        }
        
        $postData = [
            $nameField => $name,
            $emailField => $email,
            $messageField => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $googleFormUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $httpCode === 200;
        
    } catch (Exception $e) {
        logError("Google Forms submission error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send thank you email with Space Phoenix theme
 */
function sendThankYouEmail($name, $email, $message) {
    try {
        $subject = "Thank you for reaching out! 🚀";
        $emailTemplate = getSpacePhoenixEmailTemplate($name, $message);
        
        $headers = [
            'From' => getConfig('site.name') . ' <' . getConfig('site.email') . '>',
            'Reply-To' => getConfig('site.email'),
            'X-Mailer' => 'PHP/' . phpversion(),
            'MIME-Version' => '1.0',
            'Content-Type' => 'text/html; charset=UTF-8'
        ];
        
        $headerString = '';
        foreach ($headers as $key => $value) {
            $headerString .= "$key: $value\r\n";
        }
        
        return mail($email, $subject, $emailTemplate, $headerString);
        
    } catch (Exception $e) {
        logError("Thank you email error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send notification email to site owner
 */
function sendNotificationEmail($name, $email, $message, $submissionId) {
    try {
        $subject = "New Portfolio Contact: $name";
        $emailBody = "
        New contact form submission:
        
        ID: $submissionId
        Name: $name
        Email: $email
        IP: {$_SERVER['REMOTE_ADDR']}
        Time: " . date('Y-m-d H:i:s') . "
        
        Message:
        $message
        ";
        
        $headers = [
            'From' => getConfig('site.email'),
            'Reply-To' => $email,
            'X-Mailer' => 'PHP/' . phpversion(),
            'Content-Type' => 'text/plain; charset=UTF-8'
        ];
        
        $headerString = '';
        foreach ($headers as $key => $value) {
            $headerString .= "$key: $value\r\n";
        }
        
        return mail(getConfig('site.email'), $subject, $emailBody, $headerString);
        
    } catch (Exception $e) {
        logError("Notification email error: " . $e->getMessage());
        return false;
    }
}
