<?php
/**
 * Hybrid Contact Form Handler
 *
 * Handles contact form submissions with Google Forms integration,
 * database storage, and automated thank you emails
 */

// Define access constant
define('PORTFOLIO_ACCESS', true);

// Include configuration
require_once '../config/config.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Start session for CSRF protection
session_start();

try {
    // Get and sanitize input data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }
    
    $name = sanitizeInput($input['name'] ?? '');
    $email = sanitizeInput($input['email'] ?? '');
    $message = sanitizeInput($input['message'] ?? '');
    $csrfToken = $input['csrf_token'] ?? '';
    
    // Validation
    $errors = [];
    
    // CSRF token validation
    if (!verifyCSRFToken($csrfToken)) {
        $errors[] = 'Invalid security token. Please refresh the page and try again.';
    }
    
    // Name validation
    if (empty($name)) {
        $errors[] = 'Name is required.';
    } elseif (strlen($name) < 2) {
        $errors[] = 'Name must be at least 2 characters long.';
    } elseif (strlen($name) > 100) {
        $errors[] = 'Name must be less than 100 characters.';
    }
    
    // Email validation
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    } elseif (strlen($email) > 255) {
        $errors[] = 'Email address is too long.';
    }
    
    // Message validation
    if (empty($message)) {
        $errors[] = 'Message is required.';
    } elseif (strlen($message) < 10) {
        $errors[] = 'Message must be at least 10 characters long.';
    } elseif (strlen($message) > 5000) {
        $errors[] = 'Message must be less than 5000 characters.';
    }
    
    // Rate limiting (simple implementation)
    $rateLimitKey = 'contact_' . $_SERVER['REMOTE_ADDR'];
    if (isset($_SESSION[$rateLimitKey])) {
        $lastSubmission = $_SESSION[$rateLimitKey];
        if (time() - $lastSubmission < 60) { // 1 minute cooldown
            $errors[] = 'Please wait before sending another message.';
        }
    }
    
    // If there are validation errors, return them
    if (!empty($errors)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // Spam detection (basic)
    $spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations'];
    $messageWords = strtolower($message);
    foreach ($spamKeywords as $keyword) {
        if (strpos($messageWords, $keyword) !== false) {
            logError("Potential spam detected from {$_SERVER['REMOTE_ADDR']}: $keyword");
            // Don't tell the user it was flagged as spam
            echo json_encode([
                'success' => true,
                'message' => 'Thank you for your message! I\'ll get back to you soon.'
            ]);
            exit;
        }
    }
    
    // Prepare email content
    $subject = "Portfolio Contact: Message from $name";
    $emailBody = "
    New contact form submission from your portfolio:
    
    Name: $name
    Email: $email
    IP Address: {$_SERVER['REMOTE_ADDR']}
    User Agent: {$_SERVER['HTTP_USER_AGENT']}
    Timestamp: " . date('Y-m-d H:i:s') . "
    
    Message:
    $message
    
    ---
    This message was sent from your portfolio contact form.
    ";
    
    // Email headers
    $headers = [
        'From' => getConfig('mail.from_email'),
        'Reply-To' => $email,
        'X-Mailer' => 'PHP/' . phpversion(),
        'Content-Type' => 'text/plain; charset=UTF-8'
    ];
    
    $headerString = '';
    foreach ($headers as $key => $value) {
        $headerString .= "$key: $value\r\n";
    }
    
    // Send email
    $emailSent = mail(
        getConfig('site.email'),
        $subject,
        $emailBody,
        $headerString
    );
    
    if ($emailSent) {
        // Log successful submission
        logError("Contact form submitted successfully from {$_SERVER['REMOTE_ADDR']} - $name ($email)", 'INFO');
        
        // Set rate limiting
        $_SESSION[$rateLimitKey] = time();
        
        // Save to database if configured
        saveContactSubmission($name, $email, $message);
        
        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your message! I\'ll get back to you soon.'
        ]);
    } else {
        throw new Exception('Failed to send email');
    }
    
} catch (Exception $e) {
    logError("Contact form error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Sorry, there was an error sending your message. Please try again later or contact me directly.'
    ]);
}

/**
 * Save contact submission to database (if configured)
 */
function saveContactSubmission($name, $email, $message) {
    try {
        // Only save if database is configured
        if (getConfig('database.host') && getConfig('database.name')) {
            $pdo = new PDO(
                'mysql:host=' . getConfig('database.host') . ';dbname=' . getConfig('database.name') . ';charset=' . getConfig('database.charset'),
                getConfig('database.username'),
                getConfig('database.password'),
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
            
            $stmt = $pdo->prepare("
                INSERT INTO contact_submissions (name, email, message, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $name,
                $email,
                $message,
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        }
    } catch (Exception $e) {
        logError("Database error in contact form: " . $e->getMessage());
        // Don't fail the entire request if database save fails
    }
}
