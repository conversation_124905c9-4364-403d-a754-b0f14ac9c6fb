<!-- CONTACT -->
<section id="contact" class="loading">
  <div class="wrap">
    <h2>Contact</h2>
    <p class="muted">Let's connect! I'm open to new opportunities and exciting projects.</p>

    <!-- Contact Information -->
    <div style="margin-bottom: 2rem;">
      <div class="grid-2" style="gap: 2rem;">
        <div>
          <h3 style="margin-bottom: 1rem; font-size: 1.1rem;">Get in Touch</h3>
          <p class="muted" style="margin-bottom: 0.5rem;">
            <strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: var(--glow);"><EMAIL></a>
          </p>
          <p class="muted" style="margin-bottom: 0.5rem;">
            <strong>Mobile:</strong> <a href="tel:+************" style="color: var(--glow);">+91 8299681934</a>
          </p>
          <p class="muted" style="margin-bottom: 1rem;">
            <strong>Location:</strong> Lucknow, U.P., India
          </p>

          <div style="display: flex; gap: 1rem; margin-top: 1rem;">
            <a href="https://github.com/apoorv-deep" target="_blank" class="btn enhanced-hover" style="display: inline-flex; align-items: center; gap: 0.5rem;">
              <span>🔗</span> GitHub
            </a>
            <a href="https://linkedin.com/in/apoorv-deep" target="_blank" class="btn enhanced-hover" style="display: inline-flex; align-items: center; gap: 0.5rem;">
              <span>💼</span> LinkedIn
            </a>
          </div>
        </div>

        <div>
          <h3 style="margin-bottom: 1rem; font-size: 1.1rem;">Send a Message</h3>
          <!-- Custom styled form that submits to both Google Forms and our backend -->
          <form id="contactForm" method="POST" action="api/contact-hybrid.php">
            <div id="formMessages" style="margin-bottom: 1rem; display: none;"></div>
            <div style="margin-bottom: 1rem;">
              <label for="name">Name *</label>
              <input id="name" name="name" type="text" placeholder="Your Name" required maxlength="100" />
            </div>
            <div style="margin-bottom: 1rem;">
              <label for="email">Email *</label>
              <input id="email" name="email" type="email" placeholder="<EMAIL>" required maxlength="255" />
            </div>
            <div style="margin-bottom: 1rem;">
              <label for="msg">Message *</label>
              <textarea id="msg" name="message" placeholder="Tell me about your project or opportunity..." required minlength="10" maxlength="5000" rows="5"></textarea>
            </div>
            <input type="hidden" name="csrf_token" id="csrfToken" value="">
            <!-- Google Forms hidden fields (will be populated by JavaScript) -->
            <input type="hidden" name="google_form_url" id="googleFormUrl" value="">
            <input type="hidden" name="google_name_field" id="googleNameField" value="">
            <input type="hidden" name="google_email_field" id="googleEmailField" value="">
            <input type="hidden" name="google_message_field" id="googleMessageField" value="">

            <button class="btn primary enhanced-hover" type="submit" id="submitBtn">
              <span class="btn-text">Send Message</span>
              <span class="btn-loading" style="display: none;">Sending...</span>
            </button>
          </form>

          <!-- Instructions for setting up Google Forms -->
          <div id="googleFormsSetup" style="margin-top: 2rem; padding: 1rem; background: rgba(255,213,107,.1); border: 1px solid rgba(255,213,107,.3); border-radius: 8px; display: none;">
            <h4 style="margin: 0 0 0.5rem; color: var(--glow);">Google Forms Setup Required</h4>
            <p style="margin: 0; font-size: 0.9rem; color: var(--muted);">
              To enable Google Forms integration, please update the configuration in <code>config/google-forms.js</code> with your Google Form details.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
