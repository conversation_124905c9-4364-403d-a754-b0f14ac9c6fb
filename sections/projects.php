<!-- PROJECTS -->
<section id="projects" class="loading">
  <div class="wrap">
    <h2>Projects</h2>
    <p class="muted">Selected work showcasing full-stack development and system design capabilities.</p>
    <div class="projects-grid" style="margin-top:1.5rem;">
      <article class="card enhanced-hover parallax-element" data-project="foundation">
        <div class="shine" aria-hidden="true"></div>
        <div class="project-icon">🏛️</div>
        <h3>Online Foundation System</h3>
        <p>Comprehensive foundation management system for UnnyanPathFoundation with user registration, donation tracking, event management, and administrative dashboard. Features responsive design and secure data handling.</p>
        <div class="tech-stack">
          <span class="tech-badge">HTML</span>
          <span class="tech-badge">CSS</span>
          <span class="tech-badge">JavaScript</span>
          <span class="tech-badge">Bootstrap</span>
          <span class="tech-badge">Django</span>
          <span class="tech-badge">MySQL</span>
        </div>
        <a class="btn" style="margin-top:1rem;" href="https://github.com/apoorv-deep" target="_blank">View on GitHub</a>
      </article>

      <article class="card enhanced-hover parallax-element" data-project="payroll">
        <div class="shine" aria-hidden="true"></div>
        <div class="project-icon">💼</div>
        <h3>Payroll Management System</h3>
        <p>Employee information and salary management system with automated payroll calculations, attendance tracking, and comprehensive reporting. Built with modern web technologies and Ajax for seamless user experience.</p>
        <div class="tech-stack">
          <span class="tech-badge">HTML</span>
          <span class="tech-badge">CSS</span>
          <span class="tech-badge">JavaScript</span>
          <span class="tech-badge">Bootstrap</span>
          <span class="tech-badge">Ajax</span>
          <span class="tech-badge">Django</span>
          <span class="tech-badge">MySQL</span>
        </div>
        <a class="btn" style="margin-top:1rem;" href="https://github.com/apoorv-deep" target="_blank">View on GitHub</a>
      </article>

      <article class="card enhanced-hover parallax-element" data-project="portfolio">
        <div class="shine" aria-hidden="true"></div>
        <div class="project-icon">🌟</div>
        <h3>Professional Portfolio</h3>
        <p>Modern, responsive portfolio website built with modular PHP architecture. Features space-themed design, smooth animations, and optimized performance. Showcases technical skills and project experience.</p>
        <div class="tech-stack">
          <span class="tech-badge">PHP</span>
          <span class="tech-badge">HTML</span>
          <span class="tech-badge">CSS</span>
          <span class="tech-badge">JavaScript</span>
          <span class="tech-badge">GSAP</span>
          <span class="tech-badge">Responsive Design</span>
        </div>
        <a class="btn" style="margin-top:1rem;" href="https://github.com/apoorv-deep" target="_blank">View on GitHub</a>
      </article>
    </div>
  </div>
</section>

<!-- SKILLS -->
<section id="skills" class="loading">
  <div class="wrap skills">
    <div>
      <h2>Tech Stack</h2>
      <p class="muted">Technologies and tools I use to build robust web applications and systems.</p>
      <div style="margin-top:.6rem; display:flex; flex-wrap:wrap;">
        <div class="badge">PHP</div>
        <div class="badge">JavaScript</div>
        <div class="badge">Python</div>
        <div class="badge">C/C++</div>
        <div class="badge">Django</div>
        <div class="badge">Bootstrap</div>
        <div class="badge">MySQL</div>
        <div class="badge">Git</div>
        <div class="badge">GitHub</div>
        <div class="badge">VS Code</div>
        <div class="badge">Postman</div>
        <div class="badge">Ajax</div>
      </div>
    </div>
    <div class="skill-cloud" aria-hidden="true">
      <!-- Circular orbit labels, animated with Anime.js -->
      <svg viewBox="0 0 400 400">
        <defs>
          <filter id="softGlow">
            <feGaussianBlur stdDeviation="3" result="blur"/>
            <feMerge>
              <feMergeNode in="blur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        <g id="orbits" fill="none" stroke="rgba(255,255,255,.12)">
          <circle cx="200" cy="200" r="60"/>
          <circle cx="200" cy="200" r="110"/>
          <circle cx="200" cy="200" r="160"/>
        </g>
        <g id="labels" font-size="12" text-anchor="middle" fill="currentColor" filter="url(#softGlow)">
          <text x="200" y="35">PHP</text>
          <text x="330" y="90">MySQL</text>
          <text x="365" y="210">Django</text>
          <text x="320" y="320">JavaScript</text>
          <text x="200" y="365">Python</text>
          <text x="80"  y="320">Bootstrap</text>
          <text x="35"  y="210">Git</text>
          <text x="70"  y="90">VS Code</text>
        </g>
        <circle id="core" cx="200" cy="200" r="8" fill="url(#grad)"/>
      </svg>
    </div>
  </div>
</section>
