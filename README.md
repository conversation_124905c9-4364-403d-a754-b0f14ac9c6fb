# 🌟 Space Phoenix Portfolio

A modern, responsive portfolio website built with modular PHP architecture, featuring a space-themed design with smooth animations and professional backend functionality.

## ✨ Features

- **Modular PHP Architecture**: Clean, maintainable code structure
- **Space Phoenix Theme**: Immersive space-themed design with animated phoenix
- **Responsive Design**: Works perfectly on all devices
- **Contact Form**: Secure contact form with validation and spam protection
- **Performance Optimized**: Fast loading with efficient asset management
- **Accessibility**: WCAG compliant with screen reader support
- **Security**: CSRF protection, input validation, and secure coding practices
- **Database Integration**: MySQL database for contact submissions and content management

## 🚀 Technologies Used

- **Backend**: PHP 7.4+, MySQL
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Animations**: GSAP, Anime.js
- **Styling**: Custom CSS with CSS Grid and Flexbox
- **Security**: CSRF tokens, input sanitization, rate limiting

## 📋 Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/apoorv-deep/space-phoenix-portfolio.git
   cd space-phoenix-portfolio
   ```

2. **Set up the database**
   ```bash
   # Create database and user
   mysql -u root -p
   CREATE DATABASE portfolio_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'portfolio_user'@'localhost' IDENTIFIED BY 'your_secure_password';
   GRANT ALL PRIVILEGES ON portfolio_db.* TO 'portfolio_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;

   # Import schema
   mysql -u portfolio_user -p portfolio_db < database/schema.sql
   ```

3. **Configure environment**
   ```bash
   # Copy environment file
   cp config/.env.example config/.env
   
   # Edit with your actual values
   nano config/.env
   ```

4. **Set up web server**
   - Point document root to the project directory
   - Ensure PHP is enabled
   - Configure URL rewriting if needed

5. **Set permissions**
   ```bash
   # Make logs directory writable
   mkdir -p logs
   chmod 755 logs
   
   # Secure config files
   chmod 600 config/.env
   ```

## 🔧 Configuration

### Environment Variables

Update `config/.env` with your settings:

```env
# Database
DB_HOST=localhost
DB_NAME=portfolio_db
DB_USERNAME=portfolio_user
DB_PASSWORD=your_secure_password

# Email (for contact form)
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Analytics
GA_ID=your_google_analytics_id
```

### Google Analytics

Replace `GA_MEASUREMENT_ID` in the configuration with your actual Google Analytics tracking ID.

### Email Setup

For Gmail SMTP:
1. Enable 2-factor authentication
2. Generate an app password
3. Use the app password in `SMTP_PASSWORD`

## 📁 Project Structure

```
space-phoenix-portfolio/
├── api/                    # API endpoints
│   ├── contact.php        # Contact form handler
│   └── csrf-token.php     # CSRF token generator
├── assets/                # Static assets
│   ├── css/
│   │   └── style.css      # Main stylesheet
│   └── js/
│       └── main.js        # Main JavaScript
├── config/                # Configuration files
│   ├── config.php         # Main configuration
│   └── .env.example       # Environment template
├── database/              # Database files
│   └── schema.sql         # Database schema
├── includes/              # PHP includes
│   ├── header.php         # Header component
│   └── footer.php         # Footer component
├── logs/                  # Log files
├── sections/              # Page sections
│   ├── hero.php           # Hero section
│   ├── about.php          # About section
│   ├── projects.php       # Projects section
│   └── contact.php        # Contact section
└── index.php              # Main entry point
```

## 🔒 Security Features

- **CSRF Protection**: All forms protected with CSRF tokens
- **Input Validation**: Server-side validation and sanitization
- **Rate Limiting**: Prevents spam and abuse
- **Secure Headers**: Security headers for protection
- **Error Handling**: Proper error logging without information disclosure

## 🎨 Customization

### Colors and Theme

Edit CSS variables in `assets/css/style.css`:

```css
:root {
  --bg: #07070a;           /* Background */
  --panel: #0c0f16;        /* Panels */
  --text: #eaeaf2;         /* Text */
  --muted: #9aa0a6;        /* Muted text */
  --flame: #ff6b35;        /* Phoenix flame */
  --glow: #ffd56b;         /* Glow effect */
  --accent: #6c63ff;       /* Accent color */
}
```

### Content

Update content in the respective section files:
- `sections/hero.php` - Hero section content
- `sections/about.php` - About section content
- `sections/projects.php` - Projects showcase
- `sections/contact.php` - Contact information

## 📱 Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👨‍💻 Author

**Apoorv Deep Sahu**
- Email: <EMAIL>
- GitHub: [@apoorv-deep](https://github.com/apoorv-deep)
- LinkedIn: [apoorv-deep](https://linkedin.com/in/apoorv-deep)

## 🙏 Acknowledgments

- GSAP for smooth animations
- Anime.js for additional animation effects
- Lenis for smooth scrolling
- Font Awesome for icons (if used)

---

Built with ❤️ and ☕ by Apoorv Deep Sahu
