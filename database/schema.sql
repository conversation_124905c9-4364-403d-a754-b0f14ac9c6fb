-- Space Phoenix Portfolio Database Schema
-- This file contains the database structure for the portfolio application

-- Create database (run this manually if needed)
-- CREATE DATABASE portfolio_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE portfolio_db;

-- Contact submissions table
CREATE TABLE IF NOT EXISTS contact_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    status ENUM('new', 'read', 'replied', 'spam') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Projects table (for dynamic project management)
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    long_description TEXT,
    icon VARCHAR(10) DEFAULT '🚀',
    technologies JSON,
    github_url VARCHAR(500),
    live_url VARCHAR(500),
    image_url VARCHAR(500),
    featured BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'archived', 'draft') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_featured (featured),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Skills/Technologies table
CREATE TABLE IF NOT EXISTS skills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    category ENUM('language', 'framework', 'database', 'tool', 'other') DEFAULT 'other',
    proficiency ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'intermediate',
    icon VARCHAR(10),
    color VARCHAR(7), -- Hex color code
    featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_featured (featured),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Blog posts table (for future blog functionality)
CREATE TABLE IF NOT EXISTS blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL UNIQUE,
    excerpt TEXT,
    content LONGTEXT NOT NULL,
    featured_image VARCHAR(500),
    tags JSON,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_published_at (published_at),
    FULLTEXT idx_content (title, excerpt, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin users table (for content management)
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role ENUM('admin', 'editor') DEFAULT 'editor',
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default skills
INSERT INTO skills (name, category, proficiency, icon, featured, sort_order) VALUES
('PHP', 'language', 'advanced', '🐘', TRUE, 1),
('JavaScript', 'language', 'advanced', '⚡', TRUE, 2),
('Python', 'language', 'intermediate', '🐍', TRUE, 3),
('C/C++', 'language', 'intermediate', '⚙️', FALSE, 4),
('Django', 'framework', 'intermediate', '🎯', TRUE, 5),
('Bootstrap', 'framework', 'advanced', '🎨', TRUE, 6),
('MySQL', 'database', 'advanced', '🗄️', TRUE, 7),
('Git', 'tool', 'advanced', '📝', TRUE, 8),
('GitHub', 'tool', 'advanced', '🔗', FALSE, 9),
('VS Code', 'tool', 'expert', '💻', FALSE, 10),
('Postman', 'tool', 'intermediate', '📮', FALSE, 11),
('Ajax', 'technology', 'intermediate', '🔄', TRUE, 12);

-- Insert default projects
INSERT INTO projects (title, description, long_description, icon, technologies, github_url, featured, sort_order) VALUES
(
    'Online Foundation System',
    'Comprehensive foundation management system for UnnyanPathFoundation with user registration, donation tracking, event management, and administrative dashboard. Features responsive design and secure data handling.',
    'A complete web-based foundation management system built for UnnyanPathFoundation. The system includes user registration and authentication, donation tracking with payment integration, event management with RSVP functionality, and a comprehensive administrative dashboard for managing all aspects of the foundation. Built with responsive design principles and implements secure data handling practices.',
    '🏛️',
    '["HTML", "CSS", "JavaScript", "Bootstrap", "Django", "MySQL"]',
    'https://github.com/apoorv-deep',
    TRUE,
    1
),
(
    'Payroll Management System',
    'Employee information and salary management system with automated payroll calculations, attendance tracking, and comprehensive reporting. Built with modern web technologies and Ajax for seamless user experience.',
    'A comprehensive payroll management system designed for small to medium businesses. Features include employee information management, automated salary calculations based on attendance and overtime, leave management, tax calculations, and detailed reporting. The system uses Ajax for real-time updates and provides a smooth user experience.',
    '💼',
    '["HTML", "CSS", "JavaScript", "Bootstrap", "Ajax", "Django", "MySQL"]',
    'https://github.com/apoorv-deep',
    TRUE,
    2
),
(
    'Professional Portfolio',
    'Modern, responsive portfolio website built with modular PHP architecture. Features space-themed design, smooth animations, and optimized performance. Showcases technical skills and project experience.',
    'A modern, responsive portfolio website showcasing professional development skills. Built with modular PHP architecture for maintainability, features a space-themed design with smooth animations powered by GSAP, optimized performance with lazy loading and efficient asset management, and demonstrates various technical skills through interactive elements.',
    '🌟',
    '["PHP", "HTML", "CSS", "JavaScript", "GSAP", "Responsive Design"]',
    'https://github.com/apoorv-deep',
    TRUE,
    3
);

-- Insert default site settings
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_maintenance', 'false', 'boolean', 'Enable maintenance mode'),
('analytics_enabled', 'true', 'boolean', 'Enable Google Analytics tracking'),
('contact_form_enabled', 'true', 'boolean', 'Enable contact form submissions'),
('max_contact_submissions_per_hour', '5', 'number', 'Maximum contact form submissions per IP per hour');
