# Space Phoenix Portfolio Environment Configuration
# Copy this file to .env and update with your actual values

# Database Configuration
DB_HOST=localhost
DB_NAME=portfolio_db
DB_USERNAME=portfolio_user
DB_PASSWORD=your_secure_password_here

# Email Configuration (for contact form)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# Google Analytics
GA_ID=GA_MEASUREMENT_ID

# Google Forms Integration (optional)
GOOGLE_FORMS_ENABLED=false
GOOGLE_FORMS_URL=https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse
GOOGLE_FORMS_NAME_FIELD=entry.123456789
GOOGLE_FORMS_EMAIL_FIELD=entry.987654321
GOOGLE_FORMS_MESSAGE_FIELD=entry.456789123

# Security
SECRET_KEY=your_secret_key_here

# Site Configuration
SITE_URL=https://yourdomain.com
ENVIRONMENT=production

# Google Forms Integration
GOOGLE_FORMS_ENABLED=false
GOOGLE_FORMS_URL=https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse
GOOGLE_FORMS_NAME_FIELD=entry.123456789
GOOGLE_FORMS_EMAIL_FIELD=entry.987654321
GOOGLE_FORMS_MESSAGE_FIELD=entry.456789123

# Google reCAPTCHA
RECAPTCHA_ENABLED=false
RECAPTCHA_SITE_KEY=your_recaptcha_site_key
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key
