/**
 * Google Forms Integration Configuration
 * 
 * Instructions for setting up Google Forms integration:
 * 
 * 1. Create a new Google Form at https://forms.google.com
 * 2. Add the following fields:
 *    - Name (Short answer)
 *    - Email (Short answer)
 *    - Message (Paragraph)
 * 
 * 3. Get the form URL and field IDs:
 *    - Open your form in edit mode
 *    - Click "Send" button and copy the form URL
 *    - For field IDs, inspect the form HTML or use the pre-filled link method
 * 
 * 4. Update the configuration below with your actual values
 */

const GOOGLE_FORMS_CONFIG = {
  // Set to true when you have configured your Google Form
  enabled: false,
  
  // Your Google Form URL (replace with your actual form URL)
  // Example: https://docs.google.com/forms/d/e/1FAIpQLSe.../formResponse
  formUrl: 'https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse',
  
  // Field entry IDs from your Google Form
  // To find these, inspect your form or use the pre-filled link method
  fields: {
    name: 'entry.123456789',     // Replace with actual entry ID for name field
    email: 'entry.987654321',    // Replace with actual entry ID for email field
    message: 'entry.456789123'   // Replace with actual entry ID for message field
  },
  
  // Optional: Custom success/error messages
  messages: {
    success: 'Thank you for your message! I\'ll get back to you soon.',
    error: 'There was an error sending your message. Please try again.',
    networkError: 'Network error. Please check your connection and try again.'
  }
};

/**
 * How to find Google Form field IDs:
 * 
 * Method 1 - Pre-filled Link:
 * 1. In your Google Form, click the three dots menu
 * 2. Select "Get pre-filled link"
 * 3. Fill in some sample data and click "Get link"
 * 4. Copy the URL and look for entry.XXXXXXX parameters
 * 
 * Method 2 - Inspect Form:
 * 1. Open your form in a browser
 * 2. Right-click on each field and select "Inspect"
 * 3. Look for the "name" attribute with format "entry.XXXXXXX"
 * 
 * Example URL with field IDs:
 * https://docs.google.com/forms/d/e/1FAIpQLSe.../formResponse?
 * entry.123456789=John+Doe&
 * entry.987654321=<EMAIL>&
 * entry.456789123=Hello+world
 */

// Export configuration for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GOOGLE_FORMS_CONFIG;
} else if (typeof window !== 'undefined') {
  window.GOOGLE_FORMS_CONFIG = GOOGLE_FORMS_CONFIG;
}
