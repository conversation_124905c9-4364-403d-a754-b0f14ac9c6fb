/**
 * Google Forms Integration Configuration
 * 
 * Instructions for setting up Google Forms integration:
 * 
 * 1. Create a new Google Form at https://forms.google.com
 * 2. Add the following fields:
 *    - Name (Short answer)
 *    - Email (Short answer)
 *    - Message (Paragraph)
 * 
 * 3. Get the form URL and field IDs:
 *    - Open your form in edit mode
 *    - Click "Send" button and copy the form URL
 *    - For field IDs, inspect the form HTML or use the pre-filled link method
 * 
 * 4. Update the configuration below with your actual values
 */

const GOOGLE_FORMS_CONFIG = {
  // Set to true when you have configured your Google Form
  enabled: true,

  // Your Google Form URL (replace with your actual form URL)
  // Example: https://docs.google.com/forms/d/e/1FAIpQLSe.../formResponse
  formUrl: 'https://docs.google.com/forms/d/e/1FAIpQLSdEJ4SwPlbRlayCm17u-Sdk7GIh0suQel-EeUA690Znj57o8Q/formResponse',

  // Field entry IDs from your Google Form
  // To find these, inspect your form or use the pre-filled link method
  fields: {
    name: 'entry.40671465',      // Name field
    email: 'entry.98230613',     // Email field
    message: 'entry.755879383'   // Message field
  },

  // Optional: Custom success/error messages
  messages: {
    success: 'Thank you for your message! I\'ll get back to you soon. 🚀',
    error: 'There was an error sending your message. Please try again.',
    networkError: 'Network error. Please check your connection and try again.'
  }
};

// Google reCAPTCHA Configuration
const RECAPTCHA_CONFIG = {
  // Set to true when you have configured reCAPTCHA
  enabled: false,

  // Your reCAPTCHA site key (public key)
  // Get this from: https://www.google.com/recaptcha/admin
  siteKey: 'YOUR_RECAPTCHA_SITE_KEY',

  // reCAPTCHA theme ('light' or 'dark')
  theme: 'dark',

  // reCAPTCHA size ('normal', 'compact', or 'invisible')
  size: 'normal'
};

/**
 * How to find Google Form field IDs:
 * 
 * Method 1 - Pre-filled Link:
 * 1. In your Google Form, click the three dots menu
 * 2. Select "Get pre-filled link"
 * 3. Fill in some sample data and click "Get link"
 * 4. Copy the URL and look for entry.XXXXXXX parameters
 * 
 * Method 2 - Inspect Form:
 * 1. Open your form in a browser
 * 2. Right-click on each field and select "Inspect"
 * 3. Look for the "name" attribute with format "entry.XXXXXXX"
 * 
 * Example URL with field IDs:
 * https://docs.google.com/forms/d/e/1FAIpQLSe.../formResponse?
 * entry.123456789=John+Doe&
 * entry.987654321=<EMAIL>&
 * entry.456789123=Hello+world
 */

/**
 * Initialize Google Forms integration
 */
function initializeGoogleForms() {
  // Update form fields if Google Forms is enabled
  if (GOOGLE_FORMS_CONFIG.enabled) {
    const form = document.getElementById('contactForm');
    if (form) {
      document.getElementById('googleFormUrl').value = GOOGLE_FORMS_CONFIG.formUrl;
      document.getElementById('googleNameField').value = GOOGLE_FORMS_CONFIG.fields.name;
      document.getElementById('googleEmailField').value = GOOGLE_FORMS_CONFIG.fields.email;
      document.getElementById('googleMessageField').value = GOOGLE_FORMS_CONFIG.fields.message;
    }
  }

  // Initialize reCAPTCHA if enabled
  if (RECAPTCHA_CONFIG.enabled) {
    const recaptchaDiv = document.querySelector('.g-recaptcha');
    if (recaptchaDiv) {
      recaptchaDiv.setAttribute('data-sitekey', RECAPTCHA_CONFIG.siteKey);
      recaptchaDiv.setAttribute('data-theme', RECAPTCHA_CONFIG.theme);
      recaptchaDiv.setAttribute('data-size', RECAPTCHA_CONFIG.size);
    }
  } else {
    // Hide reCAPTCHA if not enabled
    const recaptchaContainer = document.querySelector('.g-recaptcha')?.parentElement;
    if (recaptchaContainer) {
      recaptchaContainer.style.display = 'none';
    }
  }
}

/**
 * Submit to Google Forms
 */
function submitToGoogleForms(formData) {
  return new Promise((resolve) => {
    if (!GOOGLE_FORMS_CONFIG.enabled) {
      resolve(false);
      return;
    }

    try {
      const googleFormData = new FormData();
      googleFormData.append(GOOGLE_FORMS_CONFIG.fields.name, formData.get('name'));
      googleFormData.append(GOOGLE_FORMS_CONFIG.fields.email, formData.get('email'));
      googleFormData.append(GOOGLE_FORMS_CONFIG.fields.message, formData.get('message'));

      fetch(GOOGLE_FORMS_CONFIG.formUrl, {
        method: 'POST',
        mode: 'no-cors',
        body: googleFormData
      }).then(() => {
        resolve(true);
      }).catch(() => {
        resolve(false);
      });
    } catch (error) {
      console.warn('Google Forms submission error:', error);
      resolve(false);
    }
  });
}

/**
 * Validate reCAPTCHA
 */
function validateRecaptcha() {
  if (!RECAPTCHA_CONFIG.enabled) {
    return true;
  }

  if (typeof grecaptcha === 'undefined') {
    return false;
  }

  const response = grecaptcha.getResponse();
  return response && response.length > 0;
}

/**
 * Reset reCAPTCHA
 */
function resetRecaptcha() {
  if (RECAPTCHA_CONFIG.enabled && typeof grecaptcha !== 'undefined') {
    grecaptcha.reset();
  }
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeGoogleForms);
} else {
  initializeGoogleForms();
}

// Export configuration and functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    GOOGLE_FORMS_CONFIG,
    RECAPTCHA_CONFIG,
    submitToGoogleForms,
    validateRecaptcha,
    resetRecaptcha
  };
} else if (typeof window !== 'undefined') {
  window.GOOGLE_FORMS_CONFIG = GOOGLE_FORMS_CONFIG;
  window.RECAPTCHA_CONFIG = RECAPTCHA_CONFIG;
  window.GoogleFormsIntegration = {
    submitToGoogleForms,
    validateRecaptcha,
    resetRecaptcha
  };
}
