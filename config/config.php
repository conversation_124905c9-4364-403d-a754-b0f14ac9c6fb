<?php
/**
 * Space Phoenix Portfolio Configuration
 * 
 * Central configuration file for the portfolio application
 */

// Prevent direct access
if (!defined('PORTFOLIO_ACCESS')) {
    die('Direct access not permitted');
}

// Define application constants
define('PORTFOLIO_VERSION', '1.0.0');
define('PORTFOLIO_NAME', 'Space Phoenix Portfolio');
define('PORTFOLIO_AUTHOR', 'Apoorv Deep Sahu');

// Environment configuration
define('ENVIRONMENT', 'development'); // development, staging, production

// Error reporting based on environment
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// Security settings
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);

// Site configuration
$config = [
    'site' => [
        'name' => 'Apoorv Deep Sahu – Backend Developer Portfolio',
        'description' => 'Backend Developer with hands-on experience building full-stack web applications, ERP systems, and CMS platforms. Skilled in PHP, Django, JavaScript, and MySQL.',
        'author' => 'Apoorv Deep Sahu',
        'email' => '<EMAIL>',
        'phone' => '+91 8299681934',
        'location' => 'Lucknow, U.P., India',
        'url' => 'https://apoorvdeep.dev', // Update with actual domain
        'timezone' => 'Asia/Kolkata'
    ],
    'social' => [
        'github' => 'https://github.com/apoorv-deep',
        'linkedin' => 'https://linkedin.com/in/apoorv-deep',
        'email' => 'mailto:<EMAIL>'
    ],
    'analytics' => [
        'google_analytics_id' => 'GA_MEASUREMENT_ID', // Replace with actual ID
        'enabled' => ENVIRONMENT === 'production'
    ],
    'database' => [
        'host' => 'localhost',
        'name' => 'portfolio_db',
        'username' => 'portfolio_user',
        'password' => '', // Set in environment variables
        'charset' => 'utf8mb4'
    ],
    'mail' => [
        'smtp_host' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_username' => '', // Set in environment variables
        'smtp_password' => '', // Set in environment variables
        'from_email' => '<EMAIL>',
        'from_name' => 'Apoorv Deep Sahu'
    ],
    'google_forms' => [
        'enabled' => false, // Set to true when configured
        'url' => '', // Google Form submission URL
        'name_field' => '', // entry.XXXXXXX for name field
        'email_field' => '', // entry.XXXXXXX for email field
        'message_field' => '' // entry.XXXXXXX for message field
    ],
    'security' => [
        'csrf_token_name' => 'csrf_token',
        'session_name' => 'phoenix_session',
        'max_login_attempts' => 5,
        'lockout_duration' => 900 // 15 minutes
    ],
    'google_forms' => [
        'enabled' => false, // Set to true when configured
        'url' => '', // Google Form submission URL
        'name_field' => '', // entry.XXXXXXX for name field
        'email_field' => '', // entry.XXXXXXX for email field
        'message_field' => '' // entry.XXXXXXX for message field
    ],
    'recaptcha' => [
        'enabled' => false, // Set to true when configured
        'site_key' => '', // Public site key
        'secret_key' => '' // Secret key (keep private)
    ]
];

// Load environment variables if available
if (file_exists(__DIR__ . '/.env')) {
    $env = parse_ini_file(__DIR__ . '/.env');
    if ($env) {
        // Override config with environment variables
        if (isset($env['DB_PASSWORD'])) {
            $config['database']['password'] = $env['DB_PASSWORD'];
        }
        if (isset($env['SMTP_USERNAME'])) {
            $config['mail']['smtp_username'] = $env['SMTP_USERNAME'];
        }
        if (isset($env['SMTP_PASSWORD'])) {
            $config['mail']['smtp_password'] = $env['SMTP_PASSWORD'];
        }
        if (isset($env['GA_ID'])) {
            $config['analytics']['google_analytics_id'] = $env['GA_ID'];
        }
        if (isset($env['GOOGLE_FORMS_ENABLED'])) {
            $config['google_forms']['enabled'] = filter_var($env['GOOGLE_FORMS_ENABLED'], FILTER_VALIDATE_BOOLEAN);
        }
        if (isset($env['GOOGLE_FORMS_URL'])) {
            $config['google_forms']['url'] = $env['GOOGLE_FORMS_URL'];
        }
        if (isset($env['GOOGLE_FORMS_NAME_FIELD'])) {
            $config['google_forms']['name_field'] = $env['GOOGLE_FORMS_NAME_FIELD'];
        }
        if (isset($env['GOOGLE_FORMS_EMAIL_FIELD'])) {
            $config['google_forms']['email_field'] = $env['GOOGLE_FORMS_EMAIL_FIELD'];
        }
        if (isset($env['GOOGLE_FORMS_MESSAGE_FIELD'])) {
            $config['google_forms']['message_field'] = $env['GOOGLE_FORMS_MESSAGE_FIELD'];
        }
    }
}

/**
 * Get configuration value
 * @param string $key Dot notation key (e.g., 'site.name')
 * @param mixed $default Default value if key not found
 * @return mixed
 */
function getConfig($key, $default = null) {
    global $config;
    
    $keys = explode('.', $key);
    $value = $config;
    
    foreach ($keys as $k) {
        if (!isset($value[$k])) {
            return $default;
        }
        $value = $value[$k];
    }
    
    return $value;
}

/**
 * Sanitize input data
 * @param mixed $data
 * @return mixed
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate CSRF token
 * @return string
 */
function generateCSRFToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    return $token;
}

/**
 * Verify CSRF token
 * @param string $token
 * @return bool
 */
function verifyCSRFToken($token) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Log error message
 * @param string $message
 * @param string $level
 */
function logError($message, $level = 'ERROR') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;
    
    $logFile = __DIR__ . '/../logs/error.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// Set default timezone
date_default_timezone_set(getConfig('site.timezone', 'UTC'));
